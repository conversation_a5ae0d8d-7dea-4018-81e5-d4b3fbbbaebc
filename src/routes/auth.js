const express = require('express');
const AuthController = require('../controllers/authController');
const { validateLoginMiddleware, validateRegisterMiddleware } = require('../inputs/authSchemas');

const router = express.Router();

/**
 * @route POST /api/v1/auth/login
 * @desc Authenticate user and return JWT token
 * @access Public
 */
router.post('/login', validateLoginMiddleware, AuthController.login);

/**
 * @route POST /api/v1/auth/register
 * @desc Register a new user
 * @access Public
 */
router.post('/register', validateRegisterMiddleware, AuthController.register);

/**
 * @route POST /api/v1/auth/verify
 * @desc Verify JWT token
 * @access Public
 */
router.post('/verify', AuthController.verifyToken);

/**
 * @route POST /api/v1/auth/logout
 * @desc Logout user (future implementation with token blacklisting)
 * @access Private
 */
router.post('/logout', AuthController.logout);

module.exports = router;
