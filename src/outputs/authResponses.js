/**
 * Authentication response schemas and formatters
 */

class AuthResponses {
  /**
   * Format login success response
   * @param {Object} user - User object
   * @param {string} token - JWT token
   * @returns {Object} Formatted response
   */
  static loginSuccess(user, token) {
    return {
      message: 'Login successful',
      data: {
        token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          created_at: user.created_at,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format registration success response
   * @param {Object} user - User object
   * @param {string} token - JWT token
   * @returns {Object} Formatted response
   */
  static registrationSuccess(user, token) {
    return {
      message: 'Registration successful',
      data: {
        token,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          created_at: user.created_at,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format token verification success response
   * @param {Object} decoded - Decoded token payload
   * @returns {Object} Formatted response
   */
  static tokenVerificationSuccess(decoded) {
    return {
      message: 'Token is valid',
      data: {
        user: {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format logout success response
   * @returns {Object} Formatted response
   */
  static logoutSuccess() {
    return {
      message: 'Logout successful',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format authentication error response
   * @param {string} message - Error message
   * @returns {Object} Formatted response
   */
  static authenticationError(message = 'Authentication failed') {
    return {
      error: message,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format validation error response
   * @param {Object} errors - Validation errors
   * @param {string} message - Error message
   * @returns {Object} Formatted response
   */
  static validationError(errors, message = 'Validation Error') {
    return {
      error: message,
      errors,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Format token error response
   * @param {string} message - Error message
   * @returns {Object} Formatted response
   */
  static tokenError(message = 'Invalid or expired token') {
    return {
      error: message,
      timestamp: new Date().toISOString(),
    };
  }
}

module.exports = AuthResponses;
