const Ajv = require('ajv');
const addFormats = require('ajv-formats');

const ajv = new Ajv({ allErrors: true });
addFormats(ajv);

// Login schema
const loginSchema = {
  type: 'object',
  properties: {
    email: {
      type: 'string',
      format: 'email',
      minLength: 1,
      maxLength: 255,
    },
    password: {
      type: 'string',
      minLength: 1,
      maxLength: 255,
    },
  },
  required: ['email', 'password'],
  additionalProperties: false,
};

// Register schema (for future use)
const registerSchema = {
  type: 'object',
  properties: {
    name: {
      type: 'string',
      minLength: 2,
      maxLength: 100,
      pattern: '^[a-zA-Z\\s]+$',
    },
    email: {
      type: 'string',
      format: 'email',
      minLength: 1,
      maxLength: 255,
    },
    password: {
      type: 'string',
      minLength: 8,
      maxLength: 255,
      pattern: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]',
    },
    role: {
      type: 'string',
      enum: ['admin', 'user', 'moderator'],
      default: 'user',
    },
  },
  required: ['name', 'email', 'password'],
  additionalProperties: false,
};

// Compile schemas
const validateLogin = ajv.compile(loginSchema);
const validateRegister = ajv.compile(registerSchema);

// Validation middleware factory
const createValidationMiddleware = (validator) => {
  return (req, res, next) => {
    const valid = validator(req.body);

    if (!valid) {
      const errors = {};
      validator.errors.forEach((error) => {
        const field = error.instancePath.replace('/', '') || error.params?.missingProperty;
        if (field) {
          errors[field] = error.message;
        } else {
          errors.general = error.message;
        }
      });

      return res.status(400).json({
        error: 'Validation Error',
        message: 'Invalid input data',
        errors,
        timestamp: new Date().toISOString(),
      });
    }

    next();
  };
};

module.exports = {
  validateLogin,
  validateRegister,
  validateLoginMiddleware: createValidationMiddleware(validateLogin),
  validateRegisterMiddleware: createValidationMiddleware(validateRegister),
};
