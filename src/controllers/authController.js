const AuthService = require('../services/authService');

class AuthController {
  /**
   * Handle user login
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async login(req, res, next) {
    try {
      const { email, password } = req.body;

      // Authenticate user
      const result = await AuthService.authenticateUser(email, password);

      if (!result.success) {
        return res.status(result.statusCode).json({
          error: result.message,
          timestamp: new Date().toISOString(),
        });
      }

      // Success response
      res.status(result.statusCode).json({
        message: result.message,
        data: result.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Handle user registration (for future use)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async register(req, res, next) {
    try {
      const userData = req.body;

      // Create user
      const result = await AuthService.createUser(userData);

      if (!result.success) {
        return res.status(result.statusCode).json({
          error: result.message,
          timestamp: new Date().toISOString(),
        });
      }

      // Success response
      res.status(result.statusCode).json({
        message: result.message,
        data: result.data,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Handle token verification (for future use)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async verifyToken(req, res, next) {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        return res.status(401).json({
          error: 'No token provided',
          timestamp: new Date().toISOString(),
        });
      }

      const decoded = AuthService.verifyToken(token);

      res.status(200).json({
        message: 'Token is valid',
        data: {
          user: decoded,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: 'Invalid or expired token',
          timestamp: new Date().toISOString(),
        });
      }
      next(error);
    }
  }

  /**
   * Handle user logout (for future use - token blacklisting)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  static async logout(req, res, next) {
    try {
      // In a real application, you might want to blacklist the token
      // For now, we'll just return a success message
      res.status(200).json({
        message: 'Logout successful',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = AuthController;
